import time
import random
import os
import csv
import tkinter as tk
from tkinter import simpledialog, messagebox
from pathlib import Path
from datetime import datetime
import re
from urllib.parse import urljoin, urlparse

# --- Use Selenium WebDriver ---
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# --- Function to get Etsy inputs via GUI ---
def get_etsy_inputs_gui():
    """Gets Etsy-specific scraping parameters."""
    root = tk.Tk()
    root.withdraw() # Hide the main tkinter window

    search_query = None
    max_products = None
    min_delay = None
    max_delay = None
    csv_filename_out = None

    # Get search query
    while not search_query:
        search_query = simpledialog.askstring("Etsy Search",
                                              "Enter Etsy search terms:\n"
                                              "Example: vintage rings, handmade jewelry",
                                              parent=root)
        if not search_query:
            messagebox.showwarning("Input Needed", "Search query cannot be empty.", parent=root)

    # Get max products to scrape
    while max_products is None:
        try:
            max_products_str = simpledialog.askstring("Input Required", "Enter Max Number of Products to Scrape:", parent=root)
            if max_products_str is None: return None # User cancelled
            max_products = int(max_products_str)
            if max_products <= 0: messagebox.showwarning("Invalid Input", "Maximum products must be positive.", parent=root); max_products = None
        except ValueError: messagebox.showerror("Invalid Input", "Please enter a valid number.", parent=root)

    # Get delays
    while min_delay is None or max_delay is None:
        try:
            min_delay_str = simpledialog.askstring("Input Required", "Enter Min Delay BETWEEN Products (secs, suggest 10+):", initialvalue="5", parent=root)
            if min_delay_str is None: return None
            min_delay = float(min_delay_str)
            max_delay_str = simpledialog.askstring("Input Required", "Enter Max Delay BETWEEN Products (secs, suggest 15+):", initialvalue="15", parent=root)
            if max_delay_str is None: return None
            max_delay = float(max_delay_str)

            if min_delay < 5 or max_delay < 8:
                messagebox.showwarning("Suggest Longer Delay", "Longer delays (e.g., 8-15s) recommended to avoid detection. Etsy has strong anti-bot measures.", parent=root)
            if min_delay <= 0 or max_delay <=0:
                messagebox.showwarning("Invalid Input", "Delays must be positive.", parent=root); min_delay, max_delay = None, None
            elif min_delay > max_delay:
                messagebox.showwarning("Invalid Input", "Minimum delay cannot be greater than maximum.", parent=root); min_delay, max_delay = None, None
        except ValueError: messagebox.showerror("Invalid Input", "Please enter valid numbers for delays.", parent=root); min_delay, max_delay = None, None

    # Get output filename
    while not csv_filename_out:
        default_name = f"etsy_{search_query.replace(' ', '_')}_analysis.csv"
        csv_filename_out = simpledialog.askstring("Input Required", "Enter Output CSV Filename:", initialvalue=default_name, parent=root)
        if not csv_filename_out: messagebox.showwarning("Input Needed", "CSV filename cannot be empty.", parent=root)
        elif not csv_filename_out.lower().endswith(".csv"): csv_filename_out += ".csv"

    root.destroy()
    return search_query, max_products, min_delay, max_delay, csv_filename_out

def setup_etsy_chrome_driver():
    """Enhanced Chrome setup specifically for Etsy using regular Selenium."""
    print("Setting up ChromeDriver for Etsy...")
    
    options = Options()
    
    # Enhanced anti-detection
    options.add_argument('--no-first-run')
    options.add_argument('--no-default-browser-check')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-extensions')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    
    # Realistic window size
    options.add_argument('--window-size=1920,1080')
    
    # User agent
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # Optional: Run headless (comment out to see browser)
    # options.add_argument('--headless')
    
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(120)
    
    # Execute script to hide webdriver properties
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def extract_product_info_from_html(html_content, product_url, search_query):
    """Extract basic product information from HTML content."""
    print(f"Extracting product info from: {product_url}")
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extract title - Updated selector for current Etsy structure
        title = 'N/A'
        title_elem = soup.find('h1')
        if title_elem:
            title = title_elem.get_text(strip=True)

        # Extract price - Updated selector for current Etsy structure
        price = 'N/A'
        # Try multiple price selectors as Etsy uses different ones
        price_selectors = [
            'p[data-test-id="lsp-price"]',
            'span[class*="currency-value"]',
            'p[class*="wt-text-title"]'
        ]
        for selector in price_selectors:
            price_elem = soup.select_one(selector)
            if price_elem:
                price = price_elem.get_text(strip=True)
                break

        # Extract image URL - Updated selector for current Etsy structure
        image_url = 'N/A'
        # Try multiple image selectors
        img_selectors = [
            'img[data-test-id="listing-page-image"]',
            'img[class*="listing-page-image"]',
            'img[alt*="vintage rings"]'
        ]
        for selector in img_selectors:
            img_elem = soup.select_one(selector)
            if img_elem:
                image_url = img_elem.get('src') or img_elem.get('data-src')
                if image_url:
                    break
        
        return {
            'search_query': search_query,
            'title': title,
            'price': price,
            'product_url': product_url,
            'image_url': image_url
        }
    except Exception as e:
        print(f"Error extracting product info: {e}")
        return None

def extract_review_data(driver):
    """
    Extracts review data by first determining the page layout (new vs. old)
    and then applying the correct scraping logic.
    """
    print("Analyzing reviews for the current page...")
    
    # --- STEP 1: Determine the page layout by checking for reviews section ---
    is_new_layout = False
    try:
        # Look for reviews section on the current page first
        reviews_section = driver.find_element(By.CSS_SELECTOR, '[data-test-id="reviews-section"], #reviews')
        print("Found reviews section on current page.")

        # Try to find "See all reviews" or similar link
        try:
            all_reviews_link = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.PARTIAL_LINK_TEXT, "See all reviews"))
            )
            print("Found 'See all reviews' link. Navigating to reviews page...")
            driver.execute_script("arguments[0].click();", all_reviews_link)
            time.sleep(random.uniform(3, 5))
            is_new_layout = True
        except TimeoutException:
            print("No 'See all reviews' link found. Reviews are on this page.")
    except:
        print("Could not find reviews section. May need to scroll or reviews may not be available.")

    # --- STEP 2: Set selectors for current Etsy structure ---
    # Updated selectors based on current Etsy review structure
    review_card_selectors = [
        "div[data-test-id='review']",
        "div[class*='review']",
        "div[data-review-id]",
        "div.wt-grid.wt-grid--block.wt-mb-xs-5"
    ]

    date_selectors = [
        'p[data-test-id="review-date"]',
        'span[class*="review-date"]',
        'p.wt-text-caption.wt-text-truncate',
        'p.wt-text-body-01'
    ]

    next_button_xpaths = [
        "//a[contains(@aria-label, 'Next page')]",
        "//button[contains(@aria-label, 'Next page')]",
        "//a[contains(@class, 'wt-btn--icon') and @aria-label='Next page']",
        "//div[@data-reviews-pagination]//a[.//span[contains(text(), 'Next page')]]"
    ]
        
        # Optional: Try to sort by "Most recent" on the old layout
    try:
            sort_trigger = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button.sort-reviews-trigger"))
            )
            driver.execute_script("arguments[0].click();", sort_trigger)
            time.sleep(1)
            most_recent_option = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-sort-option='Recency']"))
            )
            driver.execute_script("arguments[0].click();", most_recent_option)
            print("✓ Set sorting to Most Recent (Old Layout)")
            time.sleep(random.uniform(3, 5))
        except Exception:
            print("Could not set sorting for Old Layout. Proceeding with default sort.")

    # --- STEP 3: Scrape reviews using a unified loop with the correct selectors ---
    review_dates = []
    page_count = 1
    
    while True:
        print(f"Scraping reviews page {page_count}...")
        try:
            # Try to find review cards using multiple selectors
            review_cards = []
            for selector in review_card_selectors:
                try:
                    WebDriverWait(driver, 5).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    review_cards = driver.find_elements(By.CSS_SELECTOR, selector)
                    if review_cards:
                        print(f"Found {len(review_cards)} reviews on page {page_count} using selector: {selector}")
                        break
                except TimeoutException:
                    continue

            if not review_cards:
                print("No review cards found, ending review scrape for this product.")
                break

            for card in review_cards:
                # Try multiple date selectors
                date_found = False
                for date_selector in date_selectors:
                    try:
                        date_elem = card.find_element(By.CSS_SELECTOR, date_selector)
                        date_text = date_elem.text.strip()
                        if date_text:
                            review_dates.append(date_text)
                            date_found = True
                            break
                    except NoSuchElementException:
                        continue

                if not date_found:
                    # Fallback: try to find any text that looks like a date
                    try:
                        card_text = card.text
                        # Look for date patterns like "Sep 20, 2025"
                        import re
                        date_pattern = r'[A-Za-z]{3}\s+\d{1,2},\s+\d{4}'
                        dates_in_text = re.findall(date_pattern, card_text)
                        if dates_in_text:
                            review_dates.extend(dates_in_text)
                    except:
                        continue

            # Find and click the 'Next' button using multiple XPaths
            next_button_found = False
            for xpath in next_button_xpaths:
                try:
                    next_button = driver.find_element(By.XPATH, xpath)
                    # Check if button is disabled
                    button_class = next_button.get_attribute('class') or ''
                    aria_disabled = next_button.get_attribute('aria-disabled')

                    if 'wt-is-disabled' in button_class or aria_disabled == 'true':
                        print("'Next' button is disabled. Reached the end of reviews.")
                        next_button_found = False
                        break

                    print(f"Found 'Next' button using xpath: {xpath}, clicking to next page...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                    time.sleep(1)
                    driver.execute_script("arguments[0].click();", next_button)
                    time.sleep(random.uniform(3, 6))
                    page_count += 1
                    next_button_found = True
                    break
                except NoSuchElementException:
                    continue

            if not next_button_found:
                print("No 'Next' button found, reached the end of reviews.")
                break

        except TimeoutException:
            print("Timed out waiting for review cards to load. Assuming no more reviews.")
            break

    # --- STEP 4: Process the collected dates (this part is unchanged) ---
    most_recent = 'N/A'
    oldest = 'N/A'
    
    if review_dates:
        parsed_dates = []
        for date_str in review_dates:
            try:
                parsed_dates.append(datetime.strptime(date_str, '%b %d, %Y'))
            except ValueError:
                continue
        if parsed_dates:
            parsed_dates.sort()
            oldest = parsed_dates[0].strftime('%Y-%m-%d')
            most_recent = parsed_dates[-1].strftime('%Y-%m-%d')
    
    return {
        'total_reviews': len(review_dates),
        'most_recent_review_date': most_recent,
        'oldest_review_date': oldest,
    }

def scrape_etsy_products_with_reviews(search_query, max_products, min_delay, max_delay, csv_filename):
    """Main scraping function that combines product data with review analysis."""
    print("--- Starting Etsy Product & Review Analysis ---")
    print(f"Search Query: {search_query}")
    print(f"Max Products: {max_products}")
    print(f"Delay Between Products: {min_delay}-{max_delay} seconds")
    print(f"Output CSV: {csv_filename}")

    driver = None
    all_product_data = []
    
    try:
        driver = setup_etsy_chrome_driver()
        
        # --- Step 1: Collect all product URLs ---
        product_urls = []
        page = 1
        while len(product_urls) < max_products:
            encoded_query = search_query.replace(' ', '%20')
            search_url = f"https://www.etsy.com/search?q={encoded_query}&page={page}"
            print(f"\n--- Collecting URLs from Search Page {page} ---")
            driver.get(search_url)
            time.sleep(random.uniform(3, 5))

            # Scroll to load products
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight/3);")
            time.sleep(2)
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # Updated selector for current Etsy structure
            product_links = driver.find_elements(By.CSS_SELECTOR, 'a[href*="/listing/"]')
            if not product_links:
                print("No more products found on this page. Stopping URL collection.")
                break

            for link in product_links:
                if len(product_urls) < max_products:
                    product_urls.append(link.get_attribute('href'))
                else:
                    break
            
            print(f"Collected {len(product_links)} URLs. Total URLs: {len(product_urls)}")
            page += 1

        print(f"\n--- Collected a total of {len(product_urls)} product URLs to analyze. ---")

        # --- Step 2: Process each product URL ---
        for i, url in enumerate(product_urls):
            print(f"\nProcessing product {i + 1}/{len(product_urls)}: {url}")
            try:
                driver.get(url)
                time.sleep(random.uniform(min_delay, max_delay))
                
                # Extract basic product info from the product page
                product_info = extract_product_info_from_html(driver.page_source, url, search_query)
                
                if product_info:
                    # Get review data from the same page
                    review_data = extract_review_data(driver)
                    
                    # Combine data
                    combined_data = {**product_info, **review_data}
                    all_product_data.append(combined_data)
                    
                    print(f"✅ Successfully analyzed product: {product_info['title'][:50]}...")
                    print(f"    Reviews Found: {review_data['total_reviews']}, Most Recent: {review_data['most_recent_review_date']}")
                else:
                    print("❌ Could not extract product info from page.")
                    
            except Exception as e:
                print(f"❌ Critical error processing URL {url}: {e}")
                continue
        
        # --- Step 3: Save to CSV ---
        if all_product_data:
            fieldnames = [
                'search_query', 'title', 'price', 'product_url', 'image_url',
                'total_reviews', 'most_recent_review_date', 'oldest_review_date'
            ]
            
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_product_data)
            
            print(f"\n✅ Successfully saved {len(all_product_data)} products to {csv_filename}")
        
    except Exception as e:
        print(f"Critical error in main script: {e}")
        messagebox.showerror("Critical Error", f"An error occurred: {e}")
    
    finally:
        if driver:
            print("Closing browser...")
            driver.quit()
        
        print(f"\n--- Etsy Analysis Complete ---")
        print(f"Successfully analyzed {len(all_product_data)} products")
        print(f"Data saved to: {csv_filename}")

# --- Run the scraper ---
if __name__ == "__main__":
    # Check dependencies
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from bs4 import BeautifulSoup
    except ImportError as e:
        missing_lib = str(e).split("'")[-2]
        messagebox.showerror("Missing Libraries", 
                             f"Required library '{missing_lib}' not found.\n"
                             f"Please install: pip install {missing_lib}")
        print(f"ERROR: Missing required library '{missing_lib}'.")
        exit()

    inputs = get_etsy_inputs_gui()
    if inputs:
        search_query, max_products, min_delay, max_delay, csv_filename = inputs
        scrape_etsy_products_with_reviews(search_query, max_products, min_delay, max_delay, csv_filename)
    else:
        print("\nScript cancelled by user.")
