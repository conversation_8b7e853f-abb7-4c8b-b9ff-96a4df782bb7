#!/usr/bin/env python3
"""
Test script for the Etsy scraper to verify fixes work correctly.
This script tests the scraper with a small sample to validate functionality.
"""

import sys
import os
from etsy import scrape_etsy_products_with_reviews

def test_scraper():
    """Test the scraper with minimal settings."""
    print("=== Testing Etsy Scraper ===")
    
    # Test parameters
    search_query = "vintage rings"
    max_products = 2  # Small number for testing
    min_delay = 8     # Conservative delays
    max_delay = 12
    csv_filename = "test_etsy_results.csv"
    
    print(f"Testing with:")
    print(f"  Search: {search_query}")
    print(f"  Max products: {max_products}")
    print(f"  Delays: {min_delay}-{max_delay}s")
    print(f"  Output: {csv_filename}")
    print()
    
    try:
        scrape_etsy_products_with_reviews(
            search_query=search_query,
            max_products=max_products,
            min_delay=min_delay,
            max_delay=max_delay,
            csv_filename=csv_filename
        )
        
        # Check if output file was created
        if os.path.exists(csv_filename):
            print(f"\n✅ Test completed successfully!")
            print(f"✅ Output file created: {csv_filename}")
            
            # Show file size
            file_size = os.path.getsize(csv_filename)
            print(f"✅ File size: {file_size} bytes")
            
            # Show first few lines
            print(f"\n📄 First few lines of {csv_filename}:")
            with open(csv_filename, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i < 5:  # Show first 5 lines
                        print(f"  {i+1}: {line.strip()}")
                    else:
                        break
        else:
            print(f"\n❌ Test failed - no output file created")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scraper()
