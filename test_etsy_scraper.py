#!/usr/bin/env python3
"""
Test script for the Etsy scraper to verify fixes work correctly.
This script tests the scraper components and validates functionality.
"""

import sys
import os
import time

def test_imports():
    """Test that all required imports work."""
    print("=== Testing Imports ===")
    try:
        import selenium
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from bs4 import BeautifulSoup
        import tkinter as tk
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_chrome_driver():
    """Test Chrome driver setup."""
    print("\n=== Testing Chrome Driver Setup ===")
    try:
        from etsy import setup_etsy_chrome_driver
        print("✅ Chrome driver setup function imported")

        # Test driver creation (but don't actually start it)
        print("✅ Chrome driver setup function is available")
        return True
    except Exception as e:
        print(f"❌ Chrome driver test failed: {e}")
        return False

def test_html_parsing():
    """Test HTML parsing functions."""
    print("\n=== Testing HTML Parsing ===")
    try:
        from etsy import extract_product_info_from_html

        # Test with sample HTML
        sample_html = """
        <html>
            <head><title>Test Product</title></head>
            <body>
                <h1>Test Vintage Ring</h1>
                <p data-test-id="lsp-price">$25.00</p>
                <img src="test.jpg" alt="test ring">
            </body>
        </html>
        """

        result = extract_product_info_from_html(sample_html, "http://test.com", "test")

        if result and isinstance(result, dict):
            print("✅ HTML parsing function works")
            print(f"   Extracted: {result}")
            return True
        else:
            print("❌ HTML parsing returned invalid result")
            return False

    except Exception as e:
        print(f"❌ HTML parsing test failed: {e}")
        return False

def test_gui_functions():
    """Test GUI functions without actually showing GUI."""
    print("\n=== Testing GUI Functions ===")
    try:
        from etsy import get_etsy_inputs_gui
        print("✅ GUI function imported successfully")
        print("   (Note: GUI test requires manual interaction)")
        return True
    except Exception as e:
        print(f"❌ GUI function test failed: {e}")
        return False

def test_main_function():
    """Test that main scraping function can be imported."""
    print("\n=== Testing Main Function ===")
    try:
        from etsy import scrape_etsy_products_with_reviews
        print("✅ Main scraping function imported successfully")
        return True
    except Exception as e:
        print(f"❌ Main function test failed: {e}")
        return False

def run_all_tests():
    """Run all tests."""
    print("🧪 Running Etsy Scraper Tests\n")

    tests = [
        test_imports,
        test_chrome_driver,
        test_html_parsing,
        test_gui_functions,
        test_main_function
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Small delay between tests

    print(f"\n📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The scraper should work correctly.")
        print("\n💡 To run the actual scraper:")
        print("   python etsy.py")
        print("\n⚠️  Make sure Chrome browser is installed for Selenium to work.")
    else:
        print("❌ Some tests failed. Please check the errors above.")

    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
